// 通用返回按钮组件
import cloudStorage from '../../utils/cloudStorage.js';

Component({
  properties: {
    // 返回按钮的位置样式
    position: {
      type: String,
      value: 'top-left' // top-left, top-left-lower, top-right, top-center, safe-left, safe-right, below-header, avoid-avatar, auto-position, custom
    },
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    },
    // 是否显示返回按钮
    show: {
      type: Boolean,
      value: true
    },
    // 返回按钮大小
    size: {
      type: String,
      value: 'normal' // small, normal, large
    },
    // 返回类型
    backType: {
      type: String,
      value: 'auto' // auto, navigateBack, switchTab, redirectTo
    },
    // 自定义返回路径（当backType不是auto时使用）
    backPath: {
      type: String,
      value: ''
    }
  },

  data: {
    backIcon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE1IDZMMTAgMTJMMTUgMTgiIHN0cm9rZT0iI2ZmZmZmZiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHN0eWxlPgpwYXRoIHsKICBmaWx0ZXI6IGRyb3Atc2hhZG93KDAgMCAxMHB4IHJnYmEoMCwgMjEyLCAyNTUsIDAuOCkpOwp9Cjwvc3R5bGU+Cjwvc3ZnPgo='
  },

  lifetimes: {
    attached() {
      this.initBackIcon();
      // 如果使用 auto-position，自动检测最佳位置
      if (this.data.position === 'auto-position') {
        this.detectBestPosition();
      }
    }
  },

  methods: {
    // 自动检测最佳位置
    detectBestPosition() {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const route = currentPage.route;

      // 根据页面路由自动选择最佳位置
      let bestPosition = 'top-left';

      if (route.includes('user/settings') || route.includes('user/profile')) {
        bestPosition = 'avoid-avatar';
      } else if (route.includes('order/create') || route.includes('order/detail') || route.includes('chat/room')) {
        bestPosition = 'top-left-lower';
      } else if (route.includes('payment/')) {
        bestPosition = 'safe-left';
      }

      console.log('🎯 [返回按钮] 自动检测位置 - 页面:', route, '最佳位置:', bestPosition);

      this.setData({
        position: bestPosition
      });
    },

    // 初始化返回图标
    async initBackIcon() {
      try {
        // 使用云存储管理器获取返回图标
        const cloudFileId = cloudStorage.getCloudUrl('back');

        if (!cloudFileId) {
          throw new Error('返回图标配置不存在');
        }

        // 获取临时URL
        const result = await wx.cloud.getTempFileURL({
          fileList: [cloudFileId]
        });

        if (result.fileList && result.fileList.length > 0 && result.fileList[0].status === 0) {
          const backIcon = result.fileList[0].tempFileURL;
          this.setData({
            backIcon
          });
        } else {
          throw new Error('云存储图标获取失败');
        }

      } catch (error) {
        // 云存储图标获取失败时，保持使用默认图标
        console.warn('⚠️ [返回按钮] 云存储图标加载失败，使用默认图标:', error);
      }
    },

    // 处理返回按钮点击
    handleBack() {
      const { backType, backPath } = this.properties;
      
      console.log('🔙 [返回按钮] 点击返回，类型:', backType, '路径:', backPath);

      // 触发自定义事件，让父组件可以监听
      this.triggerEvent('back', {
        backType,
        backPath
      });

      // 执行返回逻辑
      switch (backType) {
        case 'navigateBack':
          wx.navigateBack({
            delta: 1,
            fail: (err) => {
              console.warn('❌ [返回按钮] navigateBack 失败:', err);
              // 如果返回失败，尝试跳转到首页
              wx.switchTab({
                url: '/pages/index/index'
              });
            }
          });
          break;

        case 'switchTab':
          if (backPath) {
            wx.switchTab({
              url: backPath,
              fail: (err) => {
                console.error('❌ [返回按钮] switchTab 失败:', err);
              }
            });
          }
          break;

        case 'redirectTo':
          if (backPath) {
            wx.redirectTo({
              url: backPath,
              fail: (err) => {
                console.error('❌ [返回按钮] redirectTo 失败:', err);
              }
            });
          }
          break;

        case 'auto':
        default:
          // 自动判断返回方式
          const pages = getCurrentPages();
          if (pages.length > 1) {
            // 有历史页面，使用 navigateBack
            wx.navigateBack({
              delta: 1,
              fail: (err) => {
                console.warn('❌ [返回按钮] auto navigateBack 失败:', err);
                // 如果返回失败，跳转到首页
                wx.switchTab({
                  url: '/pages/index/index'
                });
              }
            });
          } else {
            // 没有历史页面，跳转到首页
            wx.switchTab({
              url: '/pages/index/index'
            });
          }
          break;
      }
    }
  }
});
